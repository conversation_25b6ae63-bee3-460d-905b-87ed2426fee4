import type { BaseInfo } from '@/.generated/models'
import type { StandardItem as IStandardItem } from 'ch2-components/lib/Standard/types'

export class Standard implements BaseInfo {
  id: GUID = Guid.empty
  key?: string | null | undefined
  description?: string | null | undefined
  type = 'System.Text.Json.Nodes.JsonNode, System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51'
  isHide: boolean = false
}

export class StandardCompressor {
  private labels: string[] = []
  private values: string[] = []
  private labelMap = new Map<string, number>()
  private valueMap = new Map<string, number>()

  /* 压缩数据 */
  compress(data: IStandardItem[]): string {
    return JSON.stringify(data)
  }

  private compressItem(item: IStandardItem): any {
    return [
      item.order,
      this.getValueIndex(item.value, this.values, this.valueMap),
      this.getValueIndex(item.label, this.labels, this.labelMap),
      item.isDefault ? 1 : 0,
      item.type,
      item.remark || '',
      item.children ? item.children.map(c => this.compressItem(c)) : null,
    ]
  }

  /* 解压数据 */
  decompress(compressed?: string | null): IStandardItem[] {
    return JSON.parse(compressed ?? '[]')
  }

  private decompressItem(item: any, labels: string[], values: string[]): IStandardItem {
    return {
      order: item[0],
      value: values[item[1]]!,
      label: labels[item[2]]!,
      isDefault: item[3] === 1,
      type: item[4],
      remark: item[5] || null,
      children: item[6] ? item[6].map((c: any) => this.decompressItem(c, labels, values)) : [],
    }
  }

  /* 获取字符串索引 */
  private getValueIndex(value: string, list: string[], map: Map<string, number>): number {
    if (!map.has(value)) {
      map.set(value, list.length)
      list.push(value)
    }
    return map.get(value)!
  }
}
