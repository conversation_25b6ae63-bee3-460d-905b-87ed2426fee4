import { ChartFieldDefinition } from "./ChartFieldDefinition";
import { DatasetSourceType } from "./DatasetSourceType";
/**图表数据集（数据源）*/
export class ChartDataset {
  /**数据集名称*/
  name?: string | null | undefined = null;
  /**字段定义（字段名、类型、单位、说明等）
Json 格式存储 List[ChartFieldDefinition]*/
  fieldsJson?: ChartFieldDefinition[] | null | undefined = [];
  /**数据值（按行存储，格式为对象数组 List[List{object}]，x,y对应为FieldsJson中字段的Name，value为对应值*/
  records?: any[][] | null | undefined = [];
  /**数据来源类型（如：手动录入、Excel导入、API对接、SQL）*/
  sourceType: DatasetSourceType = 0;
  /**数据更新时间*/
  updatedAt: Dayjs = dayjs();
  /**额外信息*/
  describe?: string | null | undefined = null;
  /**数据来源url，多个回车分割*/
  url?: string | null | undefined = null;
  /**采集说明*/
  remarks?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
